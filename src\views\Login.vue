<template>
  <div class="login-page">
    <!-- 星轨背景 -->
    <div class="star-background">
      <canvas ref="starCanvas"></canvas>
      <div class="star-overlay"></div>
    </div>

    <div class="container">
      <!-- 左侧标题与特性说明 -->
      <div class="left-panel">
        <div class="title-section">
          <h1>河南农业大学<br />资料共享平台</h1>
          <p>基于前沿的AI检测审核，呈现最现代化的资料。</p>
        </div>

        <div class="features-list">
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-file-alt"></i>
            </div>
            <div class="feature-text">
              <h3>管理端翻译</h3>
              <p>支持5+种语言，协同翻译报告、稿件和文献资料</p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-key"></i>
            </div>
            <div class="feature-text">
              <h3>安全可靠</h3>
              <p>本地数据加密存储，确保信息安全，保护隐私权益</p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-user-shield"></i>
            </div>
            <div class="feature-text">
              <h3>便捷高效</h3>
              <p>服务对接河南农业大学统一身份认证，安全便捷</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧扫码登录 -->
      <div class="right-panel">
        <div class="qr-header">
          <h2>扫码登录</h2>
        </div>
        <div class="qr-content">
          <iframe class="qr-code" :src="qrLoginUrl" title="henau-qr-login"></iframe>
        </div>
        <div class="qr-footer">
          <p>试卷共享平台</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, computed } from 'vue'

const starCanvas = ref(null)

const appId = (import.meta && import.meta.env && import.meta.env.VITE_HENAU_APP_ID) || 'nd9b9131b3de588909'
const redirectUri = computed(() => (import.meta && import.meta.env && import.meta.env.VITE_HENAU_REDIRECT_URI) || `${window.location.origin}/`)

const qrLoginUrl = computed(() =>
  `https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=${appId}&redirect_uri=${encodeURIComponent(
    redirectUri.value
  )}&response_type=code&scope=henauapi_login&state=STATE`
)

function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile/i.test(
    navigator.userAgent
  ) || window.innerWidth < 768
}

onMounted(() => {
  // 手机端直接进入主界面
  if (isMobileDevice()) {
    window.location.replace('/')
    return
  }

  // 背景动画初始化（仅桌面端显示登录页时）
  const canvas = networkCanvas.value
  if (!canvas) return
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const nodes = []
  const numberOfNodes = 200
  const maxDistance = 150
  const nodeRadius = 2
  const nodeSpeed = 2

  const resizeCanvas = () => {
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
  }

  const createNodes = () => {
    nodes.length = 0
    for (let i = 0; i < numberOfNodes; i++) {
      nodes.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * nodeSpeed,
        vy: (Math.random() - 0.5) * nodeSpeed
      })
    }
  }

  let animationFrame = 0
  const draw = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    for (let i = 0; i < nodes.length; i++) {
      const node1 = nodes[i]
      node1.x += node1.vx
      node1.y += node1.vy
      if (node1.x < 0 || node1.x > canvas.width) node1.vx *= -1
      if (node1.y < 0 || node1.y > canvas.height) node1.vy *= -1

      ctx.beginPath()
      ctx.arc(node1.x, node1.y, nodeRadius, 0, Math.PI * 2)
      ctx.fillStyle = 'white'
      ctx.fill()

      for (let j = i + 1; j < nodes.length; j++) {
        const node2 = nodes[j]
        const dx = node1.x - node2.x
        const dy = node1.y - node2.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        if (distance < maxDistance) {
          ctx.beginPath()
          ctx.moveTo(node1.x, node1.y)
          ctx.lineTo(node2.x, node2.y)
          ctx.strokeStyle = `rgba(255, 255, 255, ${1 - (distance / maxDistance) * 0.8})`
          ctx.lineWidth = 0.5
          ctx.stroke()
        }
      }
    }
    animationFrame = requestAnimationFrame(draw)
  }

  resizeCanvas()
  createNodes()
  draw()
  window.addEventListener('resize', resizeCanvas)

  onBeforeUnmount(() => {
    cancelAnimationFrame(animationFrame)
    window.removeEventListener('resize', resizeCanvas)
  })
})
</script>

<style>
/* 导入字体与图标 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

.login-page {
  font-family: 'Noto Sans SC', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #000000;
  color: #333;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.network-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.network-background canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.container {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  padding: 40px;
  max-width: 1200px;
  width: 100%;
  box-sizing: border-box;
  align-items: center;
  position: relative;
  z-index: 1;
}

.left-panel {
  flex: 1;
  padding: 20px;
  min-width: 300px;
}

.title-section h1 {
  font-size: 3rem;
  color: white;
  margin-bottom: 10px;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.title-section p {
  font-size: 1.1rem;
  color: #fdfdfd;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.features-list {
  margin-top: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
  opacity: 1;
  transform: translateY(0);
}

.feature-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #000000;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.feature-text h3 {
  margin: 0;
  font-size: 1.2rem;
  color: white;
}

.feature-text p {
  margin: 5px 0 0;
  font-size: 0.9rem;
  color: #ccc;
}

.right-panel {
  width: 350px;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.qr-code {
  min-height: 400px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  border: none;
}

.qr-footer {
  margin-top: 20px;
  font-size: 0.9rem;
  color: #999;
}

@media (max-width: 800px) {
  .container {
    flex-direction: column;
    padding: 20px;
    gap: 20px;
  }
  .left-panel,
  .right-panel {
    width: 100%;
    min-width: unset;
  }
  .title-section h1 {
    font-size: 2.5rem;
  }
}

.qr-header h2 {
  font-size: 1.5rem;
  color: white;
  margin-bottom: 10px;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.qr-content {
  margin-top: 40px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 16px;
  padding: 20px;
}
</style>


