import { createRouter, createWebHistory } from 'vue-router'
import { handleAuth, isLoggedIn } from '../api/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue')
    },
    {
      path: '/',
      name: 'documents',
      component: () => import('../views/Documents.vue')
    },
    {
      path: '/document/:id',
      name: 'document-detail',
      component: () => import('../views/DocumentDetail.vue')
    },
    {
      path: '/upload',
      name: 'upload',
      component: () => import('../views/Upload.vue')
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/Profile.vue')
    },
    {
      path: '/my-downloads',
      name: 'my-downloads',
      component: () => import('../views/MyDownloads.vue')
    },
    {
      path: '/my-uploads',
      name: 'my-uploads',
      component: () => import('../views/MyUploads.vue')
    },
    {
      path: '/my-review',
      name: 'my-review',
      component: () => import('../views/my_review.vue')
    },
    {
      path: '/points-rules',
      name: 'points-rules',
      component: () => import('../views/PointsRules.vue')
    },
    {
      path: '/points-detail',
      name: 'points-detail',
      component: () => import('../views/PointsDetail.vue')
    },
    {
      path: '/admin-review',
      name: 'admin-review',
      component: () => import('../views/admin-Review.vue')
    }
  ]
})

// 路由守卫：处理 OAuth 回调并控制登录逻辑
router.beforeEach(async (to, from, next) => {
  const url = new URL(window.location.href)
  const code = url.searchParams.get('code')

  // 如果带有 code，尝试完成登录
  if (code && !isLoggedIn()) {
    try {
      await handleAuth(code)
      // 清理 URL 上的 code 参数
      url.searchParams.delete('code')
      const newUrl = url.pathname + (url.searchParams.toString() ? `?${url.searchParams.toString()}` : '')
      window.history.replaceState({}, document.title, newUrl)
      if (to.path === '/login') {
        return next('/')
      } else {
        return next()
      }
    } catch (e) {
      // 授权失败，回到登录页
      return next('/login')
    }
  }

  // 已登录或访问非受限路由
  if (to.path === '/login') {
    if (isLoggedIn()) return next('/')
    return next()
  }

  // 其他页面需要登录（根据需要可以放宽）
  if (!isLoggedIn()) {
    // 手机端直接进入首页，无需跳转登录
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile/i.test(
      navigator.userAgent
    ) || window.innerWidth < 768
    if (isMobile) return next()
    return next('/login')
  }

  next()
})

export default router 